<template>
	<view class="container">
		<!-- 表单内容 -->
		<view class="form-container">
			<view class="form-title">编辑客户资料</view>
			
			<!-- 客户姓名 -->
			<view class="form-item">
				<view class="form-label">
					<view class="label-icon">👤</view>
					<text class="label-text">客户姓名</text>
					<text class="required">*</text>
				</view>
				<view class="form-input">
					<input
						class="input-field"
						type="text"
						v-model="formData.customerName"
						placeholder="请输入客户姓名"
						maxlength="20"
					/>
					<view class="input-border"></view>
				</view>
			</view>

			<!-- 客户地址 -->
			<view class="form-item">
				<view class="form-label">
					<view class="label-icon">📍</view>
					<text class="label-text">客户地址</text>
					<text class="required">*</text>
				</view>
				<view class="form-input">
					<textarea
						class="textarea-field"
						v-model="formData.customerAddress"
						placeholder="请输入客户地址"
						maxlength="200"
						auto-height
					/>
					<view class="input-border"></view>
				</view>
			</view>

			<!-- 联系电话 -->
			<view class="form-item">
				<view class="form-label">
					<view class="label-icon">📞</view>
					<text class="label-text">联系电话</text>
					<text class="required">*</text>
				</view>
				<view class="form-input">
					<input
						class="input-field"
						type="number"
						v-model="formData.customerPhone"
						placeholder="请输入联系电话"
						maxlength="11"
					/>
					<view class="input-border"></view>
				</view>
			</view>
		</view>

		<!-- 扩展表单卡片 -->
		<view class="form-container" v-for="(formGroup, groupIndex) in extFormData" :key="groupIndex">
			<view class="form-title">{{ formGroup.form_name || '扩展信息' }}</view>

			<!-- 遍历子表单项 -->
			<view v-for="(formItem, itemIndex) in formGroup.children" :key="itemIndex" class="form-item">
				<view class="form-label">
					<!-- <view class="label-icon">{{ getFormItemIcon(formItem.form_type) }}</view> -->
					<text class="label-text">{{ formItem.field_name }}</text>
					<text class="required" v-if="formItem.require">*</text>
				</view>

				<!-- 图片类型 -->
				<view v-if="formItem.form_type === 'image'" class="form-input">
					<view class="image-upload-container">
						<!-- 显示已有图片 -->
						<view v-if="getImageList(formItem).length > 0" class="image-list">
							<view
								v-for="(image, imgIndex) in getImageList(formItem)"
								:key="imgIndex"
								class="image-item"
							>
								<image
									:src="image.url"
									class="uploaded-image"
									mode="aspectFill"
									@click="previewImage(image.url)"
								/>
								<view class="image-delete" @click="deleteImage(formItem, imgIndex)">×</view>
							</view>
						</view>
						<!-- 上传按钮 -->
						<view class="upload-btn" @click="chooseImage(formItem)">
							<text class="upload-icon">+</text>
							<text class="upload-text">上传图片</text>
						</view>
					</view>
				</view>

				<!-- 其他类型可以在这里扩展 -->
				<view v-else class="form-input">
					<input
						class="input-field"
						type="text"
						:value="formItem.value"
						:placeholder="'请输入' + formItem.field_name"
						@input="updateFormItemValue(formItem, $event)"
					/>
					<view class="input-border"></view>
				</view>
			</view>
		</view>

		<!-- 提交按钮 -->
		<view class="submit-container">
			<tn-button 
				backgroundColor="tn-main-gradient-blue" 
				fontColor="#fff" 
				width="100%"
				height="88rpx"
				:loading="submitting"
				@click="handleSubmit"
			>
				{{ submitting ? '提交中...' : '提交修改' }}
			</tn-button>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'CustomerEdit',
		data() {
			return {
				customerId: '', // 客户ID
				formData: {
					customerName: '',
					customerAddress: '',
					customerPhone: ''
				},
				extFormData: [], // 扩展表单数据
				dutyData: [], // 职责数据
				submitting: false
			}
		},
		onLoad(options) {
			// 获取客户ID
			if (options.id) {
				this.customerId = options.id
			}

			// 调用 EstateWork/profile 接口加载客户数据
			this.loadCustomerData()

			// 如果有传递的客户数据，直接使用（优先级高于接口数据）
			if (options.customerName) {
				this.formData.customerName = decodeURIComponent(options.customerName)
			}
			if (options.customerAddress) {
				this.formData.customerAddress = decodeURIComponent(options.customerAddress)
			}
			if (options.customerPhone) {
				this.formData.customerPhone = options.customerPhone
			}
		},
		methods: {
			// 加载客户数据
			loadCustomerData() {
				// 调用 EstateWork/profile 接口
				const flowCustomId = this.customerId || '2' // 从地址栏参数id获取，默认为2

				this.$api.request('EstateWork/profile', { flow_custom_id: flowCustomId }, (res) => {
					if (res.status === 'ok') {
						console.log('客户资料数据:', JSON.stringify(res))

						// 填充基本客户信息
						if (res.detail) {
							this.formData = {
								customerName: res.detail.flow_custom_name || '',
								customerAddress: res.detail.address || '',
								customerPhone: res.detail.phone || ''
							}
						}

						// 处理扩展表单数据
						if (res.ext_form && Array.isArray(res.ext_form)) {
							this.extFormData = res.ext_form
						}

						// 处理职责数据
						if (res.duty && Array.isArray(res.duty)) {
							this.dutyData = res.duty
						}

					} else {
						this.$api.toast(res.info || '加载客户数据失败')
					}
				})
			},

			// 获取表单项图标
			getFormItemIcon(formType) {
				const iconMap = {
					'image': '📷',
					'text': '📝',
					'number': '🔢',
					'date': '📅',
					'select': '📋',
					'radio': '⚪',
					'checkbox': '☑️'
				}
				return iconMap[formType] || '📄'
			},

			// 获取图片列表
			getImageList(formItem) {
				if (!formItem.complete_value) return []
				try {
					const images = JSON.parse(formItem.complete_value)
					return Array.isArray(images) ? images : []
				} catch (e) {
					return []
				}
			},

			// 选择图片
			chooseImage(formItem) {
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						// 这里应该上传图片到服务器，然后更新formItem的值
						console.log('选择的图片:', res.tempFilePaths[0])
						// 临时处理：直接添加到图片列表
						const currentImages = this.getImageList(formItem)
						currentImages.push({
							url: res.tempFilePaths[0],
							path: res.tempFilePaths[0]
						})
						formItem.complete_value = JSON.stringify(currentImages)
					}
				})
			},

			// 预览图片
			previewImage(url) {
				uni.previewImage({
					urls: [url]
				})
			},

			// 删除图片
			deleteImage(formItem, index) {
				const currentImages = this.getImageList(formItem)
				currentImages.splice(index, 1)
				formItem.complete_value = JSON.stringify(currentImages)
			},

			// 更新表单项值
			updateFormItemValue(formItem, event) {
				formItem.value = event.detail.value
			},

			// 表单验证
			validateForm() {
				if (!this.formData.customerName.trim()) {
					uni.showToast({
						title: '请输入客户姓名',
						icon: 'none'
					})
					return false
				}

				if (!this.formData.customerAddress.trim()) {
					uni.showToast({
						title: '请输入客户地址',
						icon: 'none'
					})
					return false
				}

				if (!this.formData.customerPhone.trim()) {
					uni.showToast({
						title: '请输入联系电话',
						icon: 'none'
					})
					return false
				}

				// 验证手机号格式
				const phoneReg = /^1[3-9]\d{9}$/
				if (!phoneReg.test(this.formData.customerPhone)) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					})
					return false
				}

				return true
			},

			// 提交修改
			handleSubmit() {
				if (!this.validateForm()) {
					return
				}

				this.submitting = true

				const params = {
					id: this.customerId,
					customerName: this.formData.customerName.trim(),
					customerAddress: this.formData.customerAddress.trim(),
					customerPhone: this.formData.customerPhone.trim()
				}

				console.log('提交客户资料:', params)

				// TODO: 调用修改客户资料的API
				// this.$api.request('Customer/update', params, (res) => {
				//   this.submitting = false
				//   if (res.status === 'ok') {
				//     uni.showToast({
				//       title: '修改成功',
				//       icon: 'success'
				//     })
				//     setTimeout(() => {
				//       uni.navigateBack()
				//     }, 1500)
				//   } else {
				//     this.$api.toast(res.info || '修改失败')
				//   }
				// })

				// 模拟API调用
				setTimeout(() => {
					this.submitting = false
					uni.showToast({
						title: '修改成功',
						icon: 'success'
					})
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
				}, 1000)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		padding: 20rpx;
		background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
		min-height: 100vh;
	}

	.form-container {
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 40rpx 30rpx;
		margin-bottom: 24rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
		border: 1rpx solid rgba(0, 0, 0, 0.04);
	}

	.form-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #1e293b;
		text-align: center;
		margin-bottom: 40rpx;
		position: relative;

		&::after {
			content: '';
			position: absolute;
			bottom: -12rpx;
			left: 50%;
			transform: translateX(-50%);
			width: 60rpx;
			height: 4rpx;
			background: linear-gradient(90deg, #3b82f6, #06b6d4);
			border-radius: 2rpx;
		}
	}

	.form-item {
		margin-bottom: 40rpx;
		position: relative;

		&:last-child {
			margin-bottom: 0;
		}
	}

	.form-label {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
		padding: 0 4rpx;
	}

	.label-icon {
		font-size: 32rpx;
		margin-right: 12rpx;
		width: 40rpx;
		height: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
		border-radius: 12rpx;
		border: 1rpx solid #bae6fd;
	}

	.label-text {
		font-size: 30rpx;
		color: #1e293b;
		font-weight: 600;
		letter-spacing: 0.5rpx;
		flex: 1;
	}

	.required {
		color: #ef4444;
		margin-left: 8rpx;
		font-size: 32rpx;
		font-weight: 700;
		text-shadow: 0 1rpx 2rpx rgba(239, 68, 68, 0.2);
	}

	.form-input {
		position: relative;
		background: #ffffff;
		border-radius: 16rpx;
		overflow: hidden;
	}

	.input-field {
		width: 100%;
		height: 96rpx;
		padding: 0 28rpx;
		border: 2rpx solid transparent;
		border-radius: 16rpx;
		font-size: 30rpx;
		color: #1e293b;
		background: linear-gradient(135deg, #ffffff, #f8fafc);
		box-sizing: border-box;
		transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
		position: relative;
		z-index: 2;

		&:focus {
			border-color: transparent;
			background: #ffffff;
			outline: none;
			transform: translateY(-2rpx);
			box-shadow: 0 8rpx 32rpx rgba(59, 130, 246, 0.15);
		}

		&::placeholder {
			color: #94a3b8;
			font-weight: 400;
		}
	}

	.input-border {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		border: 2rpx solid #e2e8f0;
		border-radius: 16rpx;
		transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
		pointer-events: none;
		z-index: 1;
	}

	.form-input:focus-within .input-border {
		border-color: #3b82f6;
		box-shadow: 0 0 0 6rpx rgba(59, 130, 246, 0.1);
	}

	.textarea-field {
		width: 100%;
		min-height: 160rpx;
		padding: 28rpx;
		border: 2rpx solid transparent;
		border-radius: 16rpx;
		font-size: 30rpx;
		color: #1e293b;
		background: linear-gradient(135deg, #ffffff, #f8fafc);
		box-sizing: border-box;
		resize: none;
		line-height: 1.7;
		transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
		position: relative;
		z-index: 2;

		&:focus {
			border-color: transparent;
			background: #ffffff;
			outline: none;
			transform: translateY(-2rpx);
			box-shadow: 0 8rpx 32rpx rgba(59, 130, 246, 0.15);
		}

		&::placeholder {
			color: #94a3b8;
			font-weight: 400;
		}
	}

	.submit-container {
		padding: 0 30rpx;
		margin-bottom: 60rpx;
		margin-top: 40rpx;
	}

	// 扩展表单样式
	.image-upload-container {
		display: flex;
		flex-direction: column;
		gap: 24rpx;
	}

	.image-list {
		display: flex;
		flex-wrap: wrap;
		gap: 24rpx;
	}

	.image-item {
		position: relative;
		width: 200rpx;
		height: 200rpx;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
		transition: all 0.3s ease;

		&:hover {
			transform: translateY(-4rpx);
			box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
		}
	}

	.uploaded-image {
		width: 100%;
		height: 100%;
		border-radius: 16rpx;
		border: 2rpx solid #f1f5f9;
		transition: all 0.3s ease;
	}

	.image-delete {
		position: absolute;
		top: -8rpx;
		right: -8rpx;
		width: 44rpx;
		height: 44rpx;
		background: linear-gradient(135deg, #ef4444, #dc2626);
		color: #ffffff;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 24rpx;
		font-weight: bold;
		box-shadow: 0 4rpx 12rpx rgba(239, 68, 68, 0.3);
		transition: all 0.3s ease;
		z-index: 10;

		&:active {
			transform: scale(0.95);
		}
	}

	.upload-btn {
		width: 200rpx;
		height: 200rpx;
		border: 2rpx dashed #cbd5e1;
		border-radius: 16rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background: linear-gradient(135deg, #f8fafc, #f1f5f9);
		transition: all 0.3s ease;
		position: relative;
		overflow: hidden;

		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(6, 182, 212, 0.05));
			opacity: 0;
			transition: opacity 0.3s ease;
		}

		&:active {
			transform: scale(0.98);
			border-color: #3b82f6;

			&::before {
				opacity: 1;
			}
		}
	}

	.upload-icon {
		font-size: 48rpx;
		color: #64748b;
		margin-bottom: 12rpx;
		transition: all 0.3s ease;
	}

	.upload-text {
		font-size: 24rpx;
		color: #64748b;
		font-weight: 500;
		letter-spacing: 0.5rpx;
	}

	// 输入框占位符样式
	::-webkit-input-placeholder {
		color: #94a3b8;
		font-size: 28rpx;
	}

	::-moz-placeholder {
		color: #94a3b8;
		font-size: 28rpx;
	}

	:-ms-input-placeholder {
		color: #94a3b8;
		font-size: 28rpx;
	}

	// 响应式适配
	@media screen and (max-width: 750rpx) {
		.image-item, .upload-btn {
			width: 160rpx;
			height: 160rpx;
		}

		.upload-icon {
			font-size: 40rpx;
		}

		.upload-text {
			font-size: 22rpx;
		}
	}

	// 动画效果
	@keyframes fadeInUp {
		from {
			opacity: 0;
			transform: translateY(30rpx);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	@keyframes labelPulse {
		0% { transform: scale(1); }
		50% { transform: scale(1.05); }
		100% { transform: scale(1); }
	}

	@keyframes iconBounce {
		0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
		40% { transform: translateY(-6rpx); }
		60% { transform: translateY(-3rpx); }
	}

	.form-container {
		animation: fadeInUp 0.6s ease-out;
	}

	.form-item:hover .label-icon {
		animation: iconBounce 0.6s ease-in-out;
	}

	.form-input:focus-within .label-text {
		animation: labelPulse 0.3s ease-in-out;
		color: #3b82f6;
	}

	// 表单项悬停效果
	.form-item {
		transition: all 0.3s ease;

		&:hover {
			transform: translateY(-2rpx);
		}
	}

	// 提交按钮优化
	.submit-container {
		/deep/ .tn-button {
			border-radius: 16rpx !important;
			font-weight: 600 !important;
			letter-spacing: 1rpx !important;
			box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.3) !important;
			transition: all 0.3s ease !important;

			&:active {
				transform: translateY(2rpx) !important;
				box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.2) !important;
			}
		}
	}
</style>
