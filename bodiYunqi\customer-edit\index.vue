<template>
	<view class="container">
		<!-- 表单内容 -->
		<view class="form-container">
			<view class="form-title">编辑客户资料</view>
			
			<!-- 客户姓名 -->
			<view class="form-item">
				<view class="form-label">
					<text class="label-text">客户姓名</text>
					<text class="required">*</text>
				</view>
				<view class="form-input">
					<input 
						class="input-field"
						type="text"
						v-model="formData.customerName"
						placeholder="请输入客户姓名"
						maxlength="20"
					/>
				</view>
			</view>

			<!-- 客户地址 -->
			<view class="form-item">
				<view class="form-label">
					<text class="label-text">客户地址</text>
					<text class="required">*</text>
				</view>
				<view class="form-input">
					<textarea 
						class="textarea-field"
						v-model="formData.customerAddress"
						placeholder="请输入客户地址"
						maxlength="200"
						auto-height
					/>
				</view>
			</view>

			<!-- 联系电话 -->
			<view class="form-item">
				<view class="form-label">
					<text class="label-text">联系电话</text>
					<text class="required">*</text>
				</view>
				<view class="form-input">
					<input 
						class="input-field"
						type="number"
						v-model="formData.customerPhone"
						placeholder="请输入联系电话"
						maxlength="11"
					/>
				</view>
			</view>
		</view>

		<!-- 提交按钮 -->
		<view class="submit-container">
			<tn-button 
				backgroundColor="tn-main-gradient-blue" 
				fontColor="#fff" 
				width="100%"
				height="88rpx"
				:loading="submitting"
				@click="handleSubmit"
			>
				{{ submitting ? '提交中...' : '提交修改' }}
			</tn-button>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'CustomerEdit',
		data() {
			return {
				customerId: '', // 客户ID
				formData: {
					customerName: '',
					customerAddress: '',
					customerPhone: ''
				},
				submitting: false
			}
		},
		onLoad(options) {
			// 获取客户ID
			if (options.id) {
				this.customerId = options.id
			}

			// 调用 EstateWork/profile 接口加载客户数据
			this.loadCustomerData()

			// 如果有传递的客户数据，直接使用（优先级高于接口数据）
			if (options.customerName) {
				this.formData.customerName = decodeURIComponent(options.customerName)
			}
			if (options.customerAddress) {
				this.formData.customerAddress = decodeURIComponent(options.customerAddress)
			}
			if (options.customerPhone) {
				this.formData.customerPhone = options.customerPhone
			}
		},
		methods: {
			// 加载客户数据
			loadCustomerData() {
				// 调用 EstateWork/profile 接口
				const flowCustomId = this.customerId || '2' // 从地址栏参数id获取，默认为2

				this.$api.request('EstateWork/profile', { flow_custom_id: flowCustomId }, (res) => {
					if (res.status === 'ok') {
						console.log('客户资料数据:', res)
						// 根据接口返回的数据结构填充表单
						if (res.data) {
							this.formData = {
								customerName: res.data.customerName || res.data.name || '',
								customerAddress: res.data.customerAddress || res.data.address || '',
								customerPhone: res.data.customerPhone || res.data.phone || ''
							}
						}
					} else {
						this.$api.toast(res.info || '加载客户数据失败')
					}
				})
			},

			// 表单验证
			validateForm() {
				if (!this.formData.customerName.trim()) {
					uni.showToast({
						title: '请输入客户姓名',
						icon: 'none'
					})
					return false
				}

				if (!this.formData.customerAddress.trim()) {
					uni.showToast({
						title: '请输入客户地址',
						icon: 'none'
					})
					return false
				}

				if (!this.formData.customerPhone.trim()) {
					uni.showToast({
						title: '请输入联系电话',
						icon: 'none'
					})
					return false
				}

				// 验证手机号格式
				const phoneReg = /^1[3-9]\d{9}$/
				if (!phoneReg.test(this.formData.customerPhone)) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					})
					return false
				}

				return true
			},

			// 提交修改
			handleSubmit() {
				if (!this.validateForm()) {
					return
				}

				this.submitting = true

				const params = {
					id: this.customerId,
					customerName: this.formData.customerName.trim(),
					customerAddress: this.formData.customerAddress.trim(),
					customerPhone: this.formData.customerPhone.trim()
				}

				console.log('提交客户资料:', params)

				// TODO: 调用修改客户资料的API
				// this.$api.request('Customer/update', params, (res) => {
				//   this.submitting = false
				//   if (res.status === 'ok') {
				//     uni.showToast({
				//       title: '修改成功',
				//       icon: 'success'
				//     })
				//     setTimeout(() => {
				//       uni.navigateBack()
				//     }, 1500)
				//   } else {
				//     this.$api.toast(res.info || '修改失败')
				//   }
				// })

				// 模拟API调用
				setTimeout(() => {
					this.submitting = false
					uni.showToast({
						title: '修改成功',
						icon: 'success'
					})
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
				}, 1000)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		padding: 20rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
	}

	.form-container {
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
	}

	.form-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
		text-align: center;
		margin-bottom: 40rpx;
	}

	.form-item {
		margin-bottom: 30rpx;
	}

	.form-label {
		display: flex;
		align-items: center;
		margin-bottom: 12rpx;
	}

	.label-text {
		font-size: 28rpx;
		color: #333333;
		font-weight: 500;
	}

	.required {
		color: #ff3b30;
		margin-left: 4rpx;
		font-size: 28rpx;
	}

	.form-input {
		position: relative;
	}

	.input-field {
		width: 100%;
		height: 80rpx;
		padding: 0 20rpx;
		border: 2rpx solid #e5e5e5;
		border-radius: 8rpx;
		font-size: 28rpx;
		color: #333333;
		background-color: #ffffff;
		box-sizing: border-box;

		&:focus {
			border-color: #01BEFF;
		}
	}

	.textarea-field {
		width: 100%;
		min-height: 120rpx;
		padding: 20rpx;
		border: 2rpx solid #e5e5e5;
		border-radius: 8rpx;
		font-size: 28rpx;
		color: #333333;
		background-color: #ffffff;
		box-sizing: border-box;
		resize: none;

		&:focus {
			border-color: #01BEFF;
		}
	}

	.submit-container {
		padding: 0 30rpx;
		margin-bottom: 40rpx;
	}

	// 输入框占位符样式
	::-webkit-input-placeholder {
		color: #999999;
		font-size: 28rpx;
	}

	::-moz-placeholder {
		color: #999999;
		font-size: 28rpx;
	}

	:-ms-input-placeholder {
		color: #999999;
		font-size: 28rpx;
	}
</style>
